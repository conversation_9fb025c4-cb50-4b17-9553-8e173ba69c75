import { blockBodyOptions } from 'src/data/blocks';
import type { BlockBodyOptionsType } from 'src/types/app';
import type { ItemBlock, Option } from 'src/types/models';
import { nextTick, type Ref, type ComponentPublicInstance } from 'vue';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { defaultBlocks } from 'src/data/defaultBlocks';

export const extractBlockBodyType = (itemBlock: ItemBlock): BlockBodyOptionsType => {
  return blockBodyOptions.find((option) => option.value === itemBlock.type) || blockBodyOptions[0]!;
};

// Types for utility functions
export interface BlockCreatorState {
  isDragging: Ref<boolean>;
  isCreatingBlock: Ref<boolean>;
  fabPositionLock: Ref<boolean>;
  pendingFabPosition: Ref<number | null>;
  blockCreationInProgress: Ref<boolean>;
  targetBlockId: Ref<number | null>;
}

export interface BlockCreatorStore {
  blocks: ItemBlock[];
  selectedBlockId?: string;
  currentAssessment?: any;
  totalSections: number;
  initializeBlocks: (blocks: ItemBlock[]) => void;
  addBlock: (block: ItemBlock, index: number) => void;
  appendBlock: (block: ItemBlock) => void;
  updateBlock: (block: ItemBlock, index: number) => void;
  deleteBlock: (index: number) => void;
  updateBlocksOrder: (blocks: ItemBlock[]) => void;
  validateIds: () => { valid: boolean; missing: string[] };
  validateBlockDeletion: (blockId: number) => { canDelete: boolean };
  getAssessmentId: () => number | null;
  isSectionBlock: (index: number) => boolean;
  getSectionNumber: (index: number) => number;
  getBlockRef: (id: number) => Element | ComponentPublicInstance | null;
  setBlockRef: (id: number, el: Element | ComponentPublicInstance | null) => void;
}

export interface GlobalStore {
  startSaveOperation: (message: string) => void;
  completeSaveOperation: (success: boolean, message: string) => void;
}

// FAB positioning timeout reference
let fabPositionTimeout: NodeJS.Timeout | null = null;

// ============================================================================
// FAB MANAGEMENT AND SCROLLING UTILITIES
// ============================================================================

/**
 * Scroll to the target block based on selectedBlockId
 */
export const scrollToTarget = (selectedBlockId: string | undefined, store: BlockCreatorStore) => {
  if (!selectedBlockId) return;
  const id = Number(selectedBlockId.split('-')[1]);
  const el = store.getBlockRef(id);
  if (el && 'scrollIntoView' in el) {
    (el as HTMLElement).scrollIntoView({ behavior: 'smooth', block: 'center' });
  }
};

/**
 * Debounced FAB positioning to prevent conflicts and jumping
 */
export const setFabPosition = (
  blockId: number,
  immediate: boolean,
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // If we're in the middle of creating a block, prioritize that operation
  if (state.isCreatingBlock.value && !immediate) {
    state.pendingFabPosition.value = blockId;
    return;
  }

  // Clear any pending timeout
  if (fabPositionTimeout) {
    clearTimeout(fabPositionTimeout);
  }

  if (immediate) {
    // Immediate positioning for critical operations (like new block creation)
    state.fabPositionLock.value = true;
    store.selectedBlockId = `block-${blockId}`;

    // Release lock after a brief delay
    setTimeout(() => {
      state.fabPositionLock.value = false;
      // Apply any pending position change
      if (state.pendingFabPosition.value && state.pendingFabPosition.value !== blockId) {
        setFabPosition(state.pendingFabPosition.value, false, state, store);
        state.pendingFabPosition.value = null;
      }
    }, 200);
  } else {
    // Debounced positioning for regular interactions
    fabPositionTimeout = setTimeout(() => {
      if (!state.fabPositionLock.value) {
        store.selectedBlockId = `block-${blockId}`;
      } else {
        // Store as pending if locked
        state.pendingFabPosition.value = blockId;
      }
    }, 50); // Short delay to prevent rapid changes
  }
};

/**
 * Combined FAB positioning and scrolling function
 */
export const setFabAndScroll = async (
  id: number,
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // Use immediate positioning to prevent conflicts during block creation
  setFabPosition(id, true, state, store);

  // Wait for DOM updates
  await nextTick();
  await nextTick();

  // Scroll to the target block
  scrollToTarget(store.selectedBlockId, store);
};

/**
 * AGGRESSIVE FAB focus handler that completely blocks unwanted events
 */
export const handleFocusFab = (
  blockId: number,
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // ABSOLUTE BLOCK: During block creation, REJECT ALL events except target
  if (state.blockCreationInProgress.value) {
    if (state.targetBlockId.value && blockId !== state.targetBlockId.value) {
      console.log(
        `🚫 ABSOLUTELY BLOCKING focus event from block ${blockId} during creation of block ${state.targetBlockId.value}`,
      );
      // Force back to correct position immediately
      store.selectedBlockId = `block-${state.targetBlockId.value}`;
      return;
    }
  }

  // ABSOLUTE BLOCK: Don't allow ANY repositioning if locked
  if (state.fabPositionLock.value) {
    console.log(`🚫 FAB is LOCKED - rejecting focus event from block ${blockId}`);
    return;
  }

  // Only allow positioning for legitimate user interactions
  setFabPosition(blockId, false, state, store);
};

// ============================================================================
// BLOCK UTILITY FUNCTIONS
// ============================================================================

/**
 * Helper function to determine the correct section for a new block
 */
export const getCurrentSection = (index: number, store: BlockCreatorStore): number => {
  // Find the section of the block at the given index
  const currentBlock = store.blocks[index];
  if (currentBlock) {
    return currentBlock.section;
  }

  // If no current block, find the section of the nearest previous block
  for (let i = index - 1; i >= 0; i--) {
    const block = store.blocks[i];
    if (block) {
      return block.section;
    }
  }

  // Default to section 1 if no blocks found
  return 1;
};

/**
 * Initialize blocks based on current data
 */
export const initializeBlocksFromData = async (
  props: { blocks: ItemBlock[]; type: 'quiz' | 'evaluate' },
  store: BlockCreatorStore,
) => {
  let blocksToUse: ItemBlock[] = [];

  if (props.type === 'evaluate') {
    // For evaluate type, get blocks from the block creator store
    const assessmentBlocks = store.currentAssessment?.itemBlocks;
    if (assessmentBlocks && assessmentBlocks.length > 0) {
      blocksToUse = assessmentBlocks;
    } else if (props.blocks && props.blocks.length > 0) {
      blocksToUse = props.blocks;
    } else {
      // Only use default blocks as last resort for evaluate type
      blocksToUse = defaultBlocks;
    }
  } else {
    // For quiz type, use props or default blocks
    blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  }

  store.initializeBlocks(blocksToUse);

  // Initialize with first block selected
  if (store.blocks.length > 0) {
    store.selectedBlockId = `block-${store.blocks[0]!.id}`;
    await nextTick();
    scrollToTarget(store.selectedBlockId, store);
  }
};

// ============================================================================
// DRAG AND DROP HANDLERS
// ============================================================================

/**
 * Handle drag start event
 */
export const onDragStart = (state: BlockCreatorState) => {
  state.isDragging.value = true;
};

/**
 * Handle drag end event
 */
export const onDragEnd = (state: BlockCreatorState) => {
  state.isDragging.value = false;
};

/**
 * Handle drag change event
 */
export const onDragChange = () => {
  // Handle drag change events if needed
};

// ============================================================================
// CONTENT COPYING UTILITIES
// ============================================================================

/**
 * Helper function to copy content from source block to duplicated block with proper API calls
 */
export const copyBlockContentWithBackendPersistence = async (
  source: ItemBlock,
  target: ItemBlock,
): Promise<ItemBlock> => {
  console.log('📋 Starting content copy process with backend persistence...', {
    sourceType: source.type,
    sourceId: source.id,
    targetId: target.id,
    sourceOptions: source.options?.length || 0,
    targetOptions: target.options?.length || 0,
  });

  try {
    // Import OptionService for creating options with backend persistence
    const { OptionService } = await import('src/services/asm/optionService');
    const optionService = new OptionService();

    // Create a deep copy of the target block to avoid mutations
    const updatedTarget = { ...target };

    // Copy and update questions if source has questions
    if (source.questions && source.questions.length > 0 && updatedTarget.questions) {
      console.log(
        '📝 Copying and updating questions with backend persistence...',
        source.questions.length,
      );

      const updatedQuestions = [];

      // Update existing questions with source content via backend API
      for (let index = 0; index < updatedTarget.questions.length; index++) {
        const targetQuestion = updatedTarget.questions[index];
        const sourceQuestion = source.questions[index];

        if (sourceQuestion && targetQuestion) {
          try {
            // Import QuestionService dynamically
            const questionServiceModule = await import('src/services/asm/questionService');
            const questionService = questionServiceModule.default;

            // Prepare question update data
            const questionUpdateData = {
              questionText: sourceQuestion.questionText || '', // ✅ Preserve original questionText
              isHeader: sourceQuestion.isHeader,
              sequence: sourceQuestion.sequence,
              score: sourceQuestion.score || 0,
              itemBlockId: target.id,
              // Copy optional properties if they exist
              ...(sourceQuestion.imagePath && { imagePath: sourceQuestion.imagePath }),
              ...(sourceQuestion.imageWidth && { imageWidth: sourceQuestion.imageWidth }),
              ...(sourceQuestion.imageHeight && { imageHeight: sourceQuestion.imageHeight }),
              ...(sourceQuestion.sizeLimit && { sizeLimit: sourceQuestion.sizeLimit }),
              ...(sourceQuestion.acceptFile && { acceptFile: sourceQuestion.acceptFile }),
              ...(sourceQuestion.uploadLimit && { uploadLimit: sourceQuestion.uploadLimit }),
            };

            console.log(
              `🌐 Updating question ${targetQuestion.id} via API with data:`,
              questionUpdateData,
            );
            const updatedQuestion = await questionService.updateQuestion(
              targetQuestion.id,
              questionUpdateData,
            );

            if (updatedQuestion) {
              updatedQuestions.push(updatedQuestion);
              console.log(`✅ Updated question with backend persistence ${updatedQuestion.id}:`, {
                id: updatedQuestion.id,
                questionText: updatedQuestion.questionText, // ✅ This shows the persisted questionText
                isHeader: updatedQuestion.isHeader,
                sequence: updatedQuestion.sequence,
              });
            }
          } catch (error) {
            console.warn(`⚠️ Failed to update question ${targetQuestion.id}:`, error);
            // Fallback to original question if update fails
            updatedQuestions.push(targetQuestion);
          }
        } else if (targetQuestion) {
          // Keep original question if no corresponding source question
          updatedQuestions.push(targetQuestion);
        }
      }

      // Update the target with the backend-updated questions
      if (updatedQuestions.length > 0) {
        updatedTarget.questions = updatedQuestions;
        console.log(
          `✅ Successfully updated ${updatedQuestions.length} questions with backend persistence`,
          {
            updatedQuestionsData: updatedQuestions.map((q) => ({
              id: q.id,
              questionText: q.questionText, // ✅ These are backend-persisted questionText values
              isHeader: q.isHeader,
              sequence: q.sequence,
            })),
          },
        );
      }
    }

    // Copy and create options if source has options
    if (source.options && source.options.length > 0) {
      console.log('🔘 Copying and creating options with backend persistence...', {
        sourceOptionsCount: source.options.length,
        sourceOptionsData: source.options.map((opt) => ({
          id: opt.id,
          optionText: opt.optionText,
          value: opt.value,
          sequence: opt.sequence,
        })),
      });

      const createdOptions: Option[] = [];

      // First, clear existing default options if any
      if (target.options && target.options.length > 0) {
        for (const existingOption of target.options) {
          try {
            await optionService.removeOption(existingOption.id);
          } catch (error) {
            console.warn(`⚠️ Failed to delete default option ${existingOption.id}:`, error);
          }
        }
      }

      // Then create new options based on source (preserving exact count and optionText)
      for (const sourceOption of source.options) {
        try {
          const newOptionData = {
            optionText: sourceOption.optionText || '', // ✅ Preserve original optionText
            value: sourceOption.value || 0,
            sequence: sourceOption.sequence || 1,
            itemBlockId: target.id,
            ...(sourceOption.imagePath && { imagePath: sourceOption.imagePath }),
            ...(sourceOption.nextSection && { nextSection: sourceOption.nextSection }),
          };

          const createdOption = await optionService.createOption(newOptionData);

          if (createdOption) {
            createdOptions.push(createdOption);
            console.log(`✅ Created option with backend-generated ID ${createdOption.id}:`, {
              id: createdOption.id, // ✅ Backend-generated ID
              optionText: createdOption.optionText,
              value: createdOption.value,
              sequence: createdOption.sequence,
            });
          }
        } catch (error) {
          console.warn(`⚠️ Failed to create option from source ${sourceOption.id}:`, error);
        }
      }

      // Update the target with the created options (with backend-generated IDs)
      if (createdOptions.length > 0) {
        updatedTarget.options = createdOptions;
        console.log(
          `✅ Successfully created exactly ${createdOptions.length} options with backend IDs`,
          {
            createdOptionsData: createdOptions.map((opt) => ({
              id: opt.id, // ✅ These are real backend-generated IDs
              optionText: opt.optionText,
              value: opt.value,
              sequence: opt.sequence,
            })),
          },
        );
      }
    }

    console.log('✅ Content copy process with backend persistence completed successfully', {
      finalOptionsCount: updatedTarget.options?.length || 0,
      finalQuestionsCount: updatedTarget.questions?.length || 0,
    });

    return updatedTarget;
  } catch (error) {
    console.error('❌ Error during content copy process with backend persistence:', error);
    // Return original target if copy fails
    return target;
  }
};

// ============================================================================
// BLOCK CREATION FUNCTIONS
// ============================================================================

/**
 * Handle adding a new ItemBlock after specified index
 */
export const handleAddBlockAfter = async (
  index: number,
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
  globalStore: GlobalStore,
  asm: any,
) => {
  // Prevent multiple simultaneous block creation
  if (state.isCreatingBlock.value) {
    return;
  }

  try {
    state.isCreatingBlock.value = true;
    state.blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new question...');

    // Enhanced ID validation using store helpers
    const assessmentId = props.assessmentId || asm.currentAssessment?.id;

    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      return;
    }

    // Validate that we have proper ID structure
    const validation = store.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding block:', validation.missing);
    }

    const globalIsRequired = store.currentAssessment?.globalIsRequired ?? false;
    const currentSection = getCurrentSection(index, store);

    const newBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Call backend API to create the block
    const addedBlock = await assessmentService.createBlock(newBlockData);

    if (addedBlock) {
      // AGGRESSIVE APPROACH: Set FAB position IMMEDIATELY and lock it completely
      const newBlockId = addedBlock.id;

      // Step 1: Completely disable all FAB events during creation
      state.blockCreationInProgress.value = true;
      state.targetBlockId.value = newBlockId;
      state.fabPositionLock.value = true;

      // Step 2: Force FAB position BEFORE any DOM changes
      store.selectedBlockId = `block-${newBlockId}`;

      // Step 3: Create aggressive watcher to override any position changes
      const forceCorrectPosition = () => {
        if (store.selectedBlockId !== `block-${newBlockId}`) {
          console.log(
            `🔒 FORCING FAB back to ItemBlock ${newBlockId} from ${store.selectedBlockId}`,
          );
          store.selectedBlockId = `block-${newBlockId}`;
        }
      };

      // Step 4: Set up aggressive position enforcement
      const positionEnforcer = setInterval(forceCorrectPosition, 10); // Check every 10ms

      // Step 5: Add to local store (this triggers the problematic reactivity)
      store.addBlock(addedBlock, index);

      // Step 6: Update the block creator store
      if (props.type === 'evaluate' && store.currentAssessment) {
        const currentBlocks = store.currentAssessment.itemBlocks || [];
        store.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Step 7: Complete save operation
      globalStore.completeSaveOperation(true, 'Question created successfully');

      // Step 9: Wait for DOM to settle, then scroll
      await nextTick();
      await nextTick();
      scrollToTarget(store.selectedBlockId, store);

      // Step 10: Clean up after extended period
      setTimeout(() => {
        clearInterval(positionEnforcer);
        state.fabPositionLock.value = false;
        state.blockCreationInProgress.value = false;
        state.targetBlockId.value = null;
        // Final enforcement
        store.selectedBlockId = `block-${newBlockId}`;
        console.log(`✅ FAB creation complete for ItemBlock ${newBlockId}`);
      }, 1000); // Extended cleanup period
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create question');
    }
  } catch (error) {
    console.error('❌ Error creating new block:', error);
    globalStore.completeSaveOperation(false, 'Error creating question');
  } finally {
    state.isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (state.blockCreationInProgress.value) {
      state.blockCreationInProgress.value = false;
      state.targetBlockId.value = null;
      state.fabPositionLock.value = false;
    }
  }
};

/**
 * Handle adding a new HeaderBlock after specified index
 */
export const handleAddHeaderAfter = async (
  index: number,
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
  globalStore: GlobalStore,
) => {
  // Prevent multiple simultaneous block creation
  if (state.isCreatingBlock.value) {
    return;
  }

  try {
    state.isCreatingBlock.value = true;
    state.blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new header...');

    const assessmentId = props.assessmentId || store.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      console.error('❌ Assessment ID is required for creating header block');
      return;
    }

    const currentSection = getCurrentSection(index, store);

    const newHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: false,
    };

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Call backend API to create the header block
    const addedBlock = await assessmentService.createBlock(newHeaderData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      state.targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      state.fabPositionLock.value = true;
      store.selectedBlockId = `block-${addedBlock.id}`;

      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && store.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = store.currentAssessment.itemBlocks || [];
        store.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Header created successfully');

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id, state, store);

      // Release all locks after DOM is stable
      setTimeout(() => {
        state.fabPositionLock.value = false;
        state.blockCreationInProgress.value = false;
        state.targetBlockId.value = null;
        // Final confirmation of FAB position
        store.selectedBlockId = `block-${addedBlock.id}`;
      }, 800); // Longer delay to ensure complete DOM stability
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create header');
      console.error('❌ Failed to create header block - no response from backend');
    }
  } catch (error) {
    globalStore.completeSaveOperation(false, 'Error creating header');
    console.error('❌ Error creating header block:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || store.getAssessmentId(),
      currentAssessment: !!store.currentAssessment,
      blockIndex: index,
    });
  } finally {
    state.isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (state.blockCreationInProgress.value) {
      state.blockCreationInProgress.value = false;
      state.targetBlockId.value = null;
      state.fabPositionLock.value = false;
    }
  }
};

/**
 * Handle adding a new section with header and item blocks
 */
export const handleAddSection = async (
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
  globalStore: GlobalStore,
) => {
  // Prevent multiple simultaneous section creation
  if (state.isCreatingBlock.value) {
    return;
  }

  try {
    state.isCreatingBlock.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating new section...');

    const assessmentId = props.assessmentId || store.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      console.error('❌ Assessment ID is required for creating section');
      return;
    }

    // Validate that we have proper ID structure
    const validation = store.validateIds();
    if (!validation.valid) {
      console.warn('⚠️ ID validation failed before adding section:', validation.missing);
    }

    const globalIsRequired = store.currentAssessment?.globalIsRequired ?? false;

    // Calculate the new section number
    const newSectionNumber = store.totalSections + 1;

    // Calculate the next sequence numbers based on the current blocks length
    const nextHeaderSequence = store.blocks.length + 1;
    const nextItemSequence = store.blocks.length + 2;

    // Create header block data
    const headerBlockData = {
      assessmentId: assessmentId,
      sequence: nextHeaderSequence,
      section: newSectionNumber,
      type: 'HEADER' as const,
      isRequired: false,
    };

    // Create item block data
    const itemBlockData = {
      assessmentId: assessmentId,
      sequence: nextItemSequence,
      section: newSectionNumber,
      type: 'RADIO' as const,
      isRequired: globalIsRequired,
    };

    console.log('📝 Creating new section with header and item blocks:', {
      headerBlockData,
      itemBlockData,
    });

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Create header block first
    const createdHeaderBlock = await assessmentService.createBlock(headerBlockData);
    if (!createdHeaderBlock) {
      globalStore.completeSaveOperation(false, 'Failed to create section header');
      console.error('❌ Failed to create header block for new section');
      return;
    }

    // Create item block second
    const createdItemBlock = await assessmentService.createBlock(itemBlockData);
    if (!createdItemBlock) {
      globalStore.completeSaveOperation(false, 'Failed to create section question');
      console.error('❌ Failed to create item block for new section');
      return;
    }

    console.log('✅ Section blocks successfully created:', {
      headerBlock: createdHeaderBlock,
      itemBlock: createdItemBlock,
    });

    // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
    state.fabPositionLock.value = true;
    store.selectedBlockId = `block-${createdItemBlock.id}`;

    // Append the blocks to the end of the blocks array (local store)
    store.appendBlock(createdHeaderBlock);
    store.appendBlock(createdItemBlock);

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment) {
      // Add the new blocks to the current assessment in the store
      const currentBlocks = store.currentAssessment.itemBlocks || [];
      store.currentAssessment.itemBlocks = [...currentBlocks, createdHeaderBlock, createdItemBlock];
    }

    // Complete save operation successfully
    globalStore.completeSaveOperation(true, 'Section created successfully');

    // Focus on the new ItemBlock (FAB is already locked to correct position)
    await setFabAndScroll(createdItemBlock.id, state, store);

    // Release lock after DOM is stable
    setTimeout(() => {
      state.fabPositionLock.value = false;
      // Ensure FAB is still positioned correctly
      store.selectedBlockId = `block-${createdItemBlock.id}`;
    }, 500);
  } catch (error) {
    globalStore.completeSaveOperation(false, 'Error creating section');
    console.error('❌ Error creating new section:', error);
    console.error('Error context:', {
      assessmentId: props.assessmentId || store.getAssessmentId(),
      currentAssessment: !!store.currentAssessment,
      totalSections: store.totalSections,
    });
  } finally {
    state.isCreatingBlock.value = false;
  }
};

/**
 * Handle adding a new ImageBlock
 */
export const handleAddImage = async (
  index: number,
  imageData: string,
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  try {
    const assessmentId = props.assessmentId || store.getAssessmentId();
    if (!assessmentId) {
      return;
    }

    const currentSection = getCurrentSection(index, store);

    const newImageData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
      // Include image data in a format the backend expects
      imageData: imageData,
    };

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Call backend API to create the image block
    const addedBlock = await assessmentService.createBlock(newImageData);

    if (addedBlock) {
      // Add to local store with backend response data
      store.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && store.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = store.currentAssessment.itemBlocks || [];
        store.currentAssessment.itemBlocks = [...currentBlocks, addedBlock];
      }

      await setFabAndScroll(addedBlock.id, state, store);
    }
  } catch {
    // Image creation failed silently
  }
};

// ============================================================================
// BLOCK DUPLICATION FUNCTIONS
// ============================================================================

/**
 * Handle duplicating a HeaderBlock
 */
export const handleDuplicateHeaderBlock = async (
  source: ItemBlock,
  index: number,
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  if (source.type !== 'HEADER') return;

  // Prevent multiple simultaneous block creation
  if (state.isCreatingBlock.value) {
    return;
  }

  try {
    state.isCreatingBlock.value = true;

    const assessmentId = props.assessmentId || store.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for duplicating header block');
      return;
    }

    const currentSection = getCurrentSection(index, store);

    // Create basic block data (backend will create headerBody automatically)
    const duplicateHeaderData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'HEADER' as const,
      isRequired: source.isRequired,
    };

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Call backend API to create the duplicated header block
    const duplicatedBlock = await assessmentService.createBlock(duplicateHeaderData);

    if (duplicatedBlock) {
      // Copy header body content if source has headerBody data
      if (source.headerBody && duplicatedBlock.headerBody) {
        try {
          // Use the headerBodyService to update the duplicated block's header body
          const headerBodyService = new (
            await import('src/services/asm/headerBodyService')
          ).HeaderBodyService();
          await headerBodyService.update(duplicatedBlock.headerBody.id, {
            title: source.headerBody.title || '',
            description: source.headerBody.description || '',
            itemBlockId: duplicatedBlock.id,
          });
        } catch (error) {
          console.warn('⚠️ Failed to copy header body content:', error);
          // Continue with the process even if header body copying fails
        }
      }

      // Add to local store with backend response data
      store.addBlock(duplicatedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && store.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = store.currentAssessment.itemBlocks || [];
        store.currentAssessment.itemBlocks = [...currentBlocks, duplicatedBlock];
      }

      await setFabAndScroll(duplicatedBlock.id, state, store);
    } else {
      console.error('❌ Failed to duplicate header block - no response from backend');
    }
  } catch (error) {
    console.error('❌ Error duplicating header block:', error);
  } finally {
    state.isCreatingBlock.value = false;
  }
};

/**
 * Handle duplicating a regular ItemBlock (non-header, non-image)
 */
export const handleDuplicateBlock = async (
  source: ItemBlock,
  index: number,
  props: { assessmentId?: number | null; type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  if (source.type === 'HEADER' || source.type === 'IMAGE') return;

  // Prevent multiple simultaneous block creation
  if (state.isCreatingBlock.value) {
    return;
  }

  try {
    state.isCreatingBlock.value = true;
    console.log('🚀 Starting block duplication process...', {
      sourceType: source.type,
      sourceId: source.id,
    });

    const assessmentId = props.assessmentId || store.getAssessmentId();
    if (!assessmentId) {
      console.error('❌ Assessment ID is required for duplicating block');
      return;
    }

    const currentSection = getCurrentSection(index, store);

    // Get the current global isRequired state for new blocks
    const globalIsRequired = store.currentAssessment?.globalIsRequired ?? false;

    // Create basic block data (backend will create default questions/options)
    const duplicateBlockData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: source.type,
      // Use global isRequired state for new blocks (duplicateBlock is only used for question blocks)
      isRequired: globalIsRequired,
    };

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Call backend API to create the duplicated block
    const duplicatedBlock = await assessmentService.createBlock(duplicateBlockData);

    if (duplicatedBlock) {
      // Copy content from source block to duplicated block with backend persistence
      const duplicatedBlockWithContent = await copyBlockContentWithBackendPersistence(
        source,
        duplicatedBlock,
      );

      // Add to local store with backend response data and copied content
      store.addBlock(duplicatedBlockWithContent, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && store.currentAssessment) {
        // Add the new block to the current assessment in the store
        const currentBlocks = store.currentAssessment.itemBlocks || [];
        store.currentAssessment.itemBlocks = [...currentBlocks, duplicatedBlockWithContent];
      }

      await setFabAndScroll(duplicatedBlockWithContent.id, state, store);
    } else {
      console.error('❌ Failed to duplicate block - no response from backend');
    }
  } catch (error) {
    console.error('❌ Error duplicating block:', error);
  } finally {
    state.isCreatingBlock.value = false;
  }
};

// ============================================================================
// BLOCK DELETION FUNCTIONS
// ============================================================================

/**
 * Handle deleting a block
 */
export const onClickDeleteBlock = async (
  item: ItemBlock,
  index: number,
  props: { type: 'quiz' | 'evaluate' },
  store: BlockCreatorStore,
) => {
  // Enhanced pre-deletion validation
  if (!item.id) {
    console.error('❌ Cannot delete block: Missing item ID');
    return;
  }

  if (!item.assessmentId) {
    console.error('❌ Cannot delete block: Missing assessmentId');
    return;
  }

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      return;
    }

    // Enhanced validation using block creator store
    if (props.type === 'evaluate') {
      const deletionValidation = store.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        return;
      }
    }

    // Initialize assessment service based on type
    const assessmentService = new AssessmentService(props.type);

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.deleteBlock(item);

    if (deletedBlock !== undefined) {
      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index, props, store);
    }
  } catch {
    // Error handling without notification
  }
};

/**
 * Separate function to handle UI cleanup after successful deletion
 */
export const handleBlockDeletionCleanup = async (
  item: ItemBlock,
  index: number,
  props: { type: 'quiz' | 'evaluate' },
  store: BlockCreatorStore,
  state?: BlockCreatorState,
) => {
  // Remove from local store
  store.deleteBlock(index);

  // Update the evaluate form store if this is an evaluate type
  if (props.type === 'evaluate' && store.currentAssessment) {
    const beforeFilter = store.currentAssessment.itemBlocks || [];

    // Remove the block from the current assessment in the store
    store.currentAssessment.itemBlocks = beforeFilter.filter((block) => block.id !== item.id);
  }

  // Handle focus after deletion - go to previous block (ItemBlock or HeaderBlock)
  if (store.blocks.length > 0) {
    let targetIndex;

    // Try to go to the previous block (index - 1)
    if (index > 0) {
      targetIndex = index - 1;
    }
    // If we're deleting the first block, go to the new first block (index 0)
    else {
      targetIndex = 0;
    }

    // Make sure the target index is within bounds
    targetIndex = Math.min(targetIndex, store.blocks.length - 1);

    const targetBlock = store.blocks[targetIndex];
    if (targetBlock) {
      console.log(
        `📍 After deletion, moving FAB to previous block at index ${targetIndex} (ID: ${targetBlock.id}, Type: ${targetBlock.type})`,
      );

      // Use the aggressive positioning approach for deletion as well if state is provided
      if (state) {
        state.blockCreationInProgress.value = true;
        state.targetBlockId.value = targetBlock.id;
        state.fabPositionLock.value = true;

        store.selectedBlockId = `block-${targetBlock.id}`;
        await setFabAndScroll(targetBlock.id, state, store);

        // Release locks after positioning
        setTimeout(() => {
          state.fabPositionLock.value = false;
          state.blockCreationInProgress.value = false;
          state.targetBlockId.value = null;
          console.log(
            `✅ FAB positioned on previous block ${targetBlock.id} (${targetBlock.type}) after deletion`,
          );
        }, 300);
      } else {
        // Fallback without state management
        store.selectedBlockId = `block-${targetBlock.id}`;
        scrollToTarget(store.selectedBlockId, store);
      }
    }
  } else {
    store.selectedBlockId = undefined;
  }
};

// ============================================================================
// UPDATE HANDLER FUNCTIONS
// ============================================================================

/**
 * Handle question updates from ItemBlockComponent
 */
export const handleQuestionUpdate = (
  updateData: {
    questionId?: number;
    questionText?: string;
    itemBlockId: number;
    updatedQuestion?: object;
    updatedBlock?: ItemBlock;
    typeChanged?: boolean;
  },
  props: { type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // AGGRESSIVE PROTECTION: Lock FAB during updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    state.blockCreationInProgress.value = true;
    state.targetBlockId.value = blockId;
    state.fabPositionLock.value = true;
    store.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      state.fabPositionLock.value = false;
      state.blockCreationInProgress.value = false;
      state.targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      store.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // Handle type changes
  if (updateData.typeChanged && updateData.updatedBlock) {
    console.log('🔄 Handling ItemBlock type change:', {
      blockId: updateData.itemBlockId,
      newType: updateData.updatedBlock.type,
    });

    // PROTECT FAB BEFORE DOM UPDATES
    protectFabDuringUpdate(updateData.itemBlockId);

    // Update the block type in the local store
    const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
    if (blockIndex !== -1) {
      // Replace the entire block with the updated one from backend
      store.blocks[blockIndex] = updateData.updatedBlock;
    }

    // Update the block creator store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (block: ItemBlock) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        // Replace the entire block with the updated one from backend
        store.currentAssessment.itemBlocks[assessmentBlockIndex] = updateData.updatedBlock;
      }
    }
    return;
  }

  // Handle question text updates (existing logic)
  if (updateData.questionId && updateData.questionText !== undefined) {
    // PROTECT FAB BEFORE DOM UPDATES
    protectFabDuringUpdate(updateData.itemBlockId);

    // Update the question in the local store
    const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
    if (blockIndex !== -1) {
      const block = store.blocks[blockIndex];
      if (block?.questions && block.questions.length > 0) {
        const questionIndex = block.questions.findIndex((q) => q.id === updateData.questionId);
        if (questionIndex !== -1 && block.questions[questionIndex]) {
          // Update the question text in the store
          block.questions[questionIndex].questionText = updateData.questionText;
        }
      }
    }

    // Update the block creator store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (block: ItemBlock) => block.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock = store.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.questions && assessmentBlock.questions.length > 0) {
          const questionIndex = assessmentBlock.questions.findIndex(
            (q) => q.id === updateData.questionId,
          );
          if (questionIndex !== -1 && assessmentBlock.questions[questionIndex]) {
            // Update the question text in the block creator store
            assessmentBlock.questions[questionIndex].questionText = updateData.questionText;
          }
        }
      }
    }
  }
};

/**
 * Handle option updates from ItemBlockComponent
 */
export const handleOptionUpdate = (
  updateData: {
    action: 'created' | 'updated';
    itemBlockId: number;
    option?: Option;
    optionId?: number;
    updateData?: { index: number; option: Option };
  },
  props: { type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // AGGRESSIVE PROTECTION: Lock FAB during option updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    state.blockCreationInProgress.value = true;
    state.targetBlockId.value = blockId;
    state.fabPositionLock.value = true;
    store.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      state.fabPositionLock.value = false;
      state.blockCreationInProgress.value = false;
      state.targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      store.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // PROTECT FAB BEFORE ANY DOM UPDATES
  protectFabDuringUpdate(updateData.itemBlockId);

  // Find the block in the local store
  const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
  if (blockIndex === -1) {
    console.error('❌ Block not found for option update:', updateData.itemBlockId);
    return;
  }

  const block = store.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  // Handle option creation
  if (updateData.action === 'created' && updateData.option) {
    // Initialize options array if it doesn't exist
    if (!block.options) {
      block.options = [];
    }

    // Add the new option to the block
    block.options.push(updateData.option);

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock: ItemBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock = store.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          // Initialize options array if it doesn't exist
          if (!assessmentBlock.options) {
            assessmentBlock.options = [];
          }
          // Add the new option to the assessment block
          assessmentBlock.options.push(updateData.option);
        }
      }
    }
  }

  // Handle option updates
  if (updateData.action === 'updated' && updateData.optionId && updateData.updateData) {
    const { index, option } = updateData.updateData;

    // Update the option in the local store
    if (block.options && block.options[index]) {
      block.options[index] = option;
    }

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock: ItemBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock = store.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock?.options && assessmentBlock.options[index]) {
          // Update the option in the assessment block
          assessmentBlock.options[index] = option;
        }
      }
    }
  }
};

/**
 * Handle isRequired updates from ItemBlockComponent
 */
export const handleIsRequiredUpdate = async (
  updateData: { itemBlockId: number; isRequired: boolean },
  props: { type: 'quiz' | 'evaluate' },
  state: BlockCreatorState,
  store: BlockCreatorStore,
) => {
  // AGGRESSIVE PROTECTION: Lock FAB during isRequired updates to prevent jumping
  const protectFabDuringUpdate = (blockId: number) => {
    state.blockCreationInProgress.value = true;
    state.targetBlockId.value = blockId;
    state.fabPositionLock.value = true;
    store.selectedBlockId = `block-${blockId}`;

    // Release protection after update is complete
    setTimeout(() => {
      state.fabPositionLock.value = false;
      state.blockCreationInProgress.value = false;
      state.targetBlockId.value = null;
      // Ensure FAB stays on the updated block
      store.selectedBlockId = `block-${blockId}`;
    }, 200);
  };

  // PROTECT FAB BEFORE ANY DOM UPDATES
  protectFabDuringUpdate(updateData.itemBlockId);

  // Find the block in the local store
  const blockIndex = store.blocks.findIndex((block) => block.id === updateData.itemBlockId);
  if (blockIndex === -1) {
    console.error('❌ Block not found for isRequired update:', updateData.itemBlockId);
    return;
  }

  const block = store.blocks[blockIndex];
  if (!block) {
    console.error('❌ Block is undefined:', updateData.itemBlockId);
    return;
  }

  try {
    // Update the isRequired property in the local store first (optimistic update)
    const updatedBlock = {
      ...block,
      isRequired: Boolean(updateData.isRequired),
    };
    store.updateBlock(updatedBlock, blockIndex);

    // Update the evaluate form store if this is an evaluate type
    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock: ItemBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock = store.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          // Update the isRequired property in the assessment block with proper boolean value
          assessmentBlock.isRequired = Boolean(updateData.isRequired);

          // Trigger reactivity by creating a new array reference
          // This ensures the watcher in EvaluateSettingView detects the change
          store.currentAssessment.itemBlocks = [...store.currentAssessment.itemBlocks];
        }
      }
    }

    // Call the backend API to persist the change
    console.log(`🌐 Updating isRequired via API for block ${updateData.itemBlockId}...`);
    const assessmentService = new AssessmentService(props.type);
    const apiUpdatedBlock = await assessmentService.updateBlock(updatedBlock);

    if (apiUpdatedBlock) {
      console.log(
        `✅ Successfully updated isRequired for block ${updateData.itemBlockId} to: ${updateData.isRequired}`,
      );

      // Update the local store with the response from the API (in case there are any differences)
      store.updateBlock(apiUpdatedBlock, blockIndex);

      // Update the evaluate form store with the API response
      if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
        const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
          (assessmentBlock: ItemBlock) => assessmentBlock.id === updateData.itemBlockId,
        );
        if (assessmentBlockIndex !== -1) {
          store.currentAssessment.itemBlocks[assessmentBlockIndex] = apiUpdatedBlock;
          // Trigger reactivity
          store.currentAssessment.itemBlocks = [...store.currentAssessment.itemBlocks];
        }
      }
    }
  } catch (error) {
    console.error(`❌ Failed to update isRequired for block ${updateData.itemBlockId}:`, error);

    // Revert the optimistic update on error
    store.updateBlock(block, blockIndex);

    if (props.type === 'evaluate' && store.currentAssessment?.itemBlocks) {
      const assessmentBlockIndex = store.currentAssessment.itemBlocks.findIndex(
        (assessmentBlock: ItemBlock) => assessmentBlock.id === updateData.itemBlockId,
      );
      if (assessmentBlockIndex !== -1) {
        const assessmentBlock = store.currentAssessment.itemBlocks[assessmentBlockIndex];
        if (assessmentBlock) {
          assessmentBlock.isRequired = block.isRequired;
          // Trigger reactivity
          store.currentAssessment.itemBlocks = [...store.currentAssessment.itemBlocks];
        }
      }
    }
  }
};
